# E7装备OCR助手 - 前端扁平化架构设计指南

## 项目概述

基于Vue 3 + TypeScript + Tauri 2.0架构，实现两个核心页面：**装备助手页面**和**设置页面**。采用扁平化组件结构，避免深层嵌套，提高开发效率和维护性。

## 架构设计原则

### 1. 扁平化组织结构
- 组件层级不超过3层
- 按功能职责划分组件目录
- 避免过度抽象和深层嵌套

### 2. Composables优先的状态管理
- 使用Vue 3 Composition API管理状态
- 业务逻辑封装在composables中
- 组件专注于UI展示和用户交互

### 3. 组件通信策略
- Props/Events：父子组件通信
- Provide/Inject：跨层级数据传递
- Composables：共享状态和逻辑

## 目录结构设计

```
src/
├── components/
│   ├── common/              # 通用组件
│   │   ├── NavButton.vue    # 导航按钮（已存在）
│   │   └── AdbStatusIndicator.vue  # ADB状态指示器（已存在）
│   ├── pages/               # 页面组件
│   │   ├── EquipmentPage.vue        # 装备助手页面（包含所有装备相关功能）
│   │   └── SettingsPage.vue         # 设置页面（包含所有设置功能）
│   └── layout/              # 布局组件
│       ├── AppHeader.vue    # 顶部标题栏（已存在）
│       └── Sidebar.vue      # 侧边导航栏（已存在）
├── composables/
│   ├── useNavigation.ts     # 导航状态管理（已存在）
│   ├── useAdbStatus.ts      # ADB状态管理（已存在）
│   ├── useEquipmentRecognition.ts   # 装备识别逻辑
│   └── useConfiguration.ts          # 配置管理逻辑
├── types/
│   ├── navigation.ts        # 导航类型定义（已存在）
│   ├── equipment.ts         # 装备相关类型定义
│   └── configuration.ts     # 配置相关类型定义
└── utils/
    └── tauri-api.ts         # Tauri API封装
```

## 核心Composables设计

### useApiClient
- 封装所有后端API调用
- 统一处理请求和响应
- 管理API调用状态（加载中、成功、错误）
- 提供错误处理和重试机制

## 页面组件设计

### EquipmentPage.vue - 装备助手页面

**整体布局**：双列布局（50%/50%），左右分栏显示

**左侧列功能**：
- **控制区域**：页面标题"装备识别助手"和识别按钮
- **分数显示区域**：装备分数显示、颜色状态、强化建议
- **装备信息区域**：装备基本信息、主属性和副属性列表

**右侧列功能**：
- **截图预览区域**：显示处理后的装备截图，支持图片自适应缩放
- **运行日志区域**：显示运行日志信息，支持滚动查看和文本选择

**核心功能**：
- 触发装备识别流程和状态显示
- 格式化装备数据展示
- 图片加载状态处理
- 日志信息管理

### SettingsPage.vue - 设置页面

**整体布局**：垂直布局，从上到下的区域分隔

**页面头部**：
- 标题"系统设置"和保存按钮
- 状态指示器显示初始化状态

**设置区域功能**：
- **ADB连接设置**：端口配置、超时设置、自动连接开关、连接测试
- **热键设置**：热键启用开关、热键输入和配置
- **OCR识别设置**：页面识别和装备识别参数配置
- **装备分数标准设置**：分数标准表格、左三/右三装备标准、速度装备和重铸标准

**核心功能**：
- 配置参数的读取、修改和保存
- 实时配置验证和状态反馈
- 设置项的分组管理和界面展示

## 后端API集成

### Tauri命令封装
- `adb_check_connection`: ADB连接检查
- `equipment_recognize`: 装备识别
- `load_config`: 加载配置
- `save_config`: 保存配置


### API调用策略
- 统一错误处理
- 加载状态管理
- 结果数据格式化

## 实现步骤

### 第一阶段：基础类型和API客户端
1. 创建装备相关类型定义 (`types/equipment.ts`)
2. 创建配置相关类型定义 (`types/configuration.ts`)
3. 创建Tauri API封装工具 (`utils/tauri-api.ts`)
4. 实现API客户端Composable (`composables/useApiClient.ts`)

### 第二阶段：页面组件实现
5. 创建装备助手页面 (`components/pages/EquipmentPage.vue`)
   - 集成装备捕获、展示、分数、截图预览和日志功能
   - 实现双列布局和响应式设计
   - 直接调用后端API获取数据

6. 创建设置页面 (`components/pages/SettingsPage.vue`)
   - 集成ADB、热键、OCR和装备分数标准设置
   - 实现垂直布局和配置管理
   - 直接调用后端API进行配置操作

### 第三阶段：页面集成和测试
7. 更新主应用组件 (`App.vue`) 以使用新的页面组件
8. 测试页面功能和API调用
9. 优化样式和用户体验

## 样式设计指南

### 布局规范
- 装备助手页面：双列布局（50%/50%）
- 设置页面：垂直布局，区域分隔
- 使用CSS Grid和Flexbox实现响应式布局

### 视觉规范
- 遵循需求.md中的UI设计
- 支持深色/浅色主题切换
- 使用CSS变量管理主题色彩
- 统一的间距和圆角规范

### 组件样式
- 使用scoped CSS避免样式冲突
- 组件内部样式自包含
- 通过CSS变量支持主题定制

## 开发规范

### 代码规范
- 使用TypeScript严格模式
- 统一的命名规范
- 完整的类型定义

### 组件规范
- 单一职责原则
- Props和Events明确定义
- 完整的错误边界处理

### 测试策略
- 组件单元测试
- Composables逻辑测试
- 集成测试覆盖主要流程

## 总结

本架构设计遵循扁平化原则，通过合理的组件划分和状态管理，实现了高内聚、低耦合的前端架构。每个组件职责单一，易于开发、测试和维护。通过Composables封装业务逻辑，提高了代码复用性和可测试性。整体架构支持快速迭代和功能扩展，满足项目的长期发展需求。
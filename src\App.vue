<script setup lang="ts">
import AppHeader from './components/AppHeader.vue'
import Sidebar from './components/Sidebar.vue'
import EquipmentPage from './pages/EquipmentPage.vue'
import SettingsPage from './pages/SettingsPage.vue'
import { useNavigation } from './composables/useNavigation'
import type { NavigationItem } from './types/navigation'

// 使用导航状态管理
const { currentPage, setCurrentPage } = useNavigation()

// 处理导航切换
const handleNavigate = (page: NavigationItem) => {
  setCurrentPage(page)
}
</script>

<template>
  <div class="app-container">
    <!-- 顶部标题栏 -->
    <AppHeader />

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 左侧导航栏 -->
      <Sidebar
        @navigate="handleNavigate"
      />

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 装备助手页面 -->
        <EquipmentPage v-if="currentPage === 'equipment'" />
        <!-- 设置页面 -->
        <SettingsPage v-else-if="currentPage === 'settings'" />
        <!-- 默认页面占位符 -->
        <div v-else class="page-placeholder">
          <h2>页面未找到</h2>
          <p>请选择一个有效的页面</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.app-container {
  display: grid;
  grid-template-rows: 50px 1fr;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.main-content {
  display: grid;
  grid-template-columns: 60px 1fr;
  overflow: hidden;
}

.content-area {
  padding: 20px;
  background: #ffffff;
  overflow-y: auto;
}

.page-placeholder {
  max-width: 800px;
  margin: 0 auto;
}

.page-placeholder h2 {
  color: #2c3e50;
  margin-bottom: 16px;
  font-size: 24px;
  font-weight: 600;
}

.page-placeholder p {
  color: #6c757d;
  font-size: 16px;
  line-height: 1.5;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .content-area {
    background: #1e1e1e;
  }

  .page-placeholder h2 {
    color: #f6f6f6;
  }

  .page-placeholder p {
    color: #adb5bd;
  }
}
</style>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #2c3e50;
  background-color: #ffffff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  html, body {
    color: #f6f6f6;
    background-color: #1e1e1e;
  }
}
</style>
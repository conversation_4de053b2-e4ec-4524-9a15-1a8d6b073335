use image::{DynamicImage, GrayImage, GenericImageView};
use imageproc::template_matching::{match_template, MatchTemplateMethod, find_extremes};
use anyhow::{Context, Result};
use std::path::Path;
use serde::{Deserialize, Serialize};

use crate::config::ConfigService;
use crate::image::ImageRegion;
use crate::ocr::{OcrService, OcrScenario};

/// 页面类型枚举
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum PageType {
    /// 背包页面
    Backpack,
    /// 强化页面
    Enhancement,
    /// 重铸页面
    Recast,
    /// 其他页面
    Other,
}

/// 页面识别结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PageRecognitionResult {
    pub page_type: PageType,
    pub confidence: f32,
}

/// 页面识别服务
pub struct DetectionService {
    config: ConfigService,
    ocr_service: OcrService,
    backpack_template: Option<GrayImage>,
    recast_template: Option<GrayImage>,
}

impl DetectionService {
    /// 创建新的页面识别服务
    pub fn new() -> Result<Self> {
        let config = ConfigService::new("config.json".to_string());
        let ocr_service = OcrService::new()?;
        Ok(Self {
            config,
            ocr_service,
            backpack_template: None,
            recast_template: None,
        })
    }

    /// 使用指定配置创建页面识别服务
    pub fn with_config(config: ConfigService) -> Result<Self> {
        let ocr_service = OcrService::with_config(config.clone());
        Ok(Self {
            config,
            ocr_service,
            backpack_template: None,
            recast_template: None,
        })
    }

    /// 初始化模板图像
    pub fn initialize_templates(&mut self) -> Result<()> {
        // 加载背包标志模板
        let backpack_path = Path::new("src-tauri/pages_sign/Backpack_sign.png");
        if backpack_path.exists() {
            let backpack_img = image::open(backpack_path)
                .context("加载背包标志模板失败")?;
            self.backpack_template = Some(backpack_img.to_luma8());
        }

        // 加载重铸标志模板
        let recast_path = Path::new("src-tauri/pages_sign/Recast_sign.png");
        if recast_path.exists() {
            let recast_img = image::open(recast_path)
                .context("加载重铸标志模板失败")?;
            self.recast_template = Some(recast_img.to_luma8());
        }

        Ok(())
    }

    /// 验证图像分辨率是否为1280x720
    pub fn validate_resolution(&self, image: &DynamicImage) -> Result<()> {
        let (width, height) = image.dimensions();
        if width != 1280 || height != 720 {
            return Err(anyhow::anyhow!(
                "图像分辨率不正确，期望1280x720，实际{}x{}",
                width, height
            ));
        }
        Ok(())
    }

    /// 识别页面类型
    pub async fn recognize_page_type(&self, image_path: &Path) -> Result<PageRecognitionResult> {
        // 加载图像
        let image = image::open(image_path)
            .context("加载图像失败")?;
        
        // 验证分辨率
        self.validate_resolution(&image)?;

        // 第一步：截取页面标题区域进行OCR识别
        let title_region = ImageRegion::new(60, 15, 115, 35);
        let title_image = self.extract_region(&image, &title_region)?;
        
        // 调用OCR服务识别标题区域文本
        let ocr_text = self.perform_ocr_recognition(&title_image).await?;
        
        let page_type = if ocr_text.contains("强化装备") {
            PageType::Enhancement
        } else if ocr_text.contains("背包") || ocr_text.contains("钢铁工坊") {
            // 需要进一步识别背包和重铸页面
            self.distinguish_backpack_recast(&image).await?
        } else {
            PageType::Other
        };
        
        Ok(PageRecognitionResult {
            page_type,
            confidence: 0.95, // 模拟置信度
        })
    }

    /// 区分背包和重铸页面
    async fn distinguish_backpack_recast(&self, image: &DynamicImage) -> Result<PageType> {
        // 截取标志区域
        let sign_region = ImageRegion::new(25, 160, 80, 55);
        let sign_image = self.extract_region(image, &sign_region)?;
        let gray_sign = sign_image.to_luma8();

        let mut best_match = PageType::Other;
        let mut best_score = 0.0;

        // 与背包模板匹配
        if let Some(ref backpack_template) = self.backpack_template {
            let score = self.template_match_score(&gray_sign, backpack_template)?;
            if score > best_score {
                best_score = score;
                best_match = PageType::Backpack;
            }
        }

        // 与重铸模板匹配
        if let Some(ref recast_template) = self.recast_template {
            let score = self.template_match_score(&gray_sign, recast_template)?;
            if score > best_score {
                best_score = score;
                best_match = PageType::Recast;
            }
        }

        // 如果匹配分数太低，返回其他页面
        if best_score < 0.7 {
            best_match = PageType::Other;
        }

        Ok(best_match)
    }

    /// 模板匹配计算分数
    fn template_match_score(&self, image: &GrayImage, template: &GrayImage) -> Result<f32> {
        if image.width() < template.width() || image.height() < template.height() {
            return Ok(0.0);
        }

        let result = match_template(image, template, MatchTemplateMethod::CrossCorrelationNormalized);
        let extremes = find_extremes(&result);
        
        Ok(extremes.max_value)
    }

    /// 提取图像区域
    fn extract_region(&self, image: &DynamicImage, region: &ImageRegion) -> Result<DynamicImage> {
        let cropped = image.crop_imm(region.x, region.y, region.width, region.height);
        Ok(cropped)
    }

    /// 执行OCR识别
    async fn perform_ocr_recognition(&self, image: &DynamicImage) -> Result<String> {
        // 保存临时图像文件用于OCR识别
        let temp_path = std::env::temp_dir().join("temp_ocr_image.png");
        image.save(&temp_path)
            .context("保存临时图像失败")?;

        // 使用OCR服务进行页面检测场景的识别
        let ocr_result = self.ocr_service.recognize_text(&temp_path, OcrScenario::PageDetection).await?;

        // 清理临时文件
        if temp_path.exists() {
            let _ = std::fs::remove_file(&temp_path);
        }

        // 提取识别到的文本内容
        let text_content = self.ocr_service.extract_text_content(&ocr_result);
        let combined_text = text_content.join(" ");

        Ok(combined_text)
    }
}

/// Tauri命令接口

/// 识别页面类型
#[tauri::command]
pub async fn recognize_page_type(image_path: String) -> Result<PageRecognitionResult, String> {
    let processor = DetectionService::new()
        .map_err(|e| e.to_string())?;
    
    let path = Path::new(&image_path);
    processor.recognize_page_type(path).await
        .map_err(|e| e.to_string())
}

/// 初始化页面识别服务模板
#[tauri::command]
pub async fn initialize_detection_service() -> Result<(), String> {
    let mut processor = DetectionService::new()
        .map_err(|e| e.to_string())?;
    
    processor.initialize_templates()
        .map_err(|e| e.to_string())
}



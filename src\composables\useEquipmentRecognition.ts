/**
 * 装备识别 composable
 * 简化版本，直接调用后端API
 */

import { invoke } from '@tauri-apps/api/core'
import type { EquipmentRecognitionResult } from '../types/equipment'

export const useEquipmentRecognition = () => {
  /**
   * 执行装备识别
   */
  const recognizeEquipment = async (): Promise<EquipmentRecognitionResult> => {
    return await invoke<EquipmentRecognitionResult>('equipment_recognize')
  }

  return {
    recognizeEquipment
  }
}
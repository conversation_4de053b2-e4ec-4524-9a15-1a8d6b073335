use std::path::Path;
use serde::{Deserialize, Serialize};

use crate::detection::{DetectionService, PageRecognitionResult, PageType};
use crate::image::ImageService;
use crate::equipment::{Equipment, EquipmentParser};
use crate::evaluator::{EquipmentEvaluator, ScoreResult};
use crate::ocr::{OcrResult, OcrScenario};

/// 装备识别完整结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EquipmentRecognitionResult {
    pub equipment: Equipment,
    pub evaluation: ScoreResult,
    pub page_recognition: PageRecognitionResult,
}

/// 统一的装备识别流程函数 - 从截图到完整结果
#[tauri::command]
pub async fn recognize_equipment_from_screen() -> Result<EquipmentRecognitionResult, String> {
    // 1. 截图
    let mut adb_service = crate::adb::AdbService::new()
        .map_err(|e| format!("ADB服务初始化失败: {}", e))?;
    let screenshot_path = adb_service.take_screenshot().await
        .map_err(|e| format!("截图失败: {}", e))?;
    
    // 2. 页面识别
    let mut detection_service = DetectionService::new()
        .map_err(|e| format!("检测服务初始化失败: {}", e))?;
    detection_service.initialize_templates()
        .map_err(|e| format!("模板初始化失败: {}", e))?;
    
    let path = Path::new(&screenshot_path);
    let recognition_result = detection_service.recognize_page_type(path).await
        .map_err(|e| format!("页面识别失败: {}", e))?;
    
    // 检查是否为支持的页面类型
    if recognition_result.page_type == PageType::Other {
        return Err("不支持的页面类型，请确保截图显示装备详情页面".to_string());
    }
    
    // 3. 图像处理
    let image_service = ImageService::new()
        .map_err(|e| format!("图像服务初始化失败: {}", e))?;
    let processing_result = image_service.process_page_image(path, &recognition_result.page_type).await
        .map_err(|e| format!("图像处理失败: {}", e))?;
    
    // 4. OCR识别
    let ocr_service = crate::ocr::OcrService::new()
        .map_err(|e| format!("OCR服务初始化失败: {}", e))?;
    let ocr_result = ocr_service.recognize_text(&processing_result.processed_image_path, OcrScenario::EquipmentDetection).await
        .map_err(|e| format!("OCR识别失败: {}", e))?;
    
    // 5. 装备信息解析
    let parser = EquipmentParser::new();
    let page_type = recognition_result.page_type.clone();
    let equipment = parser.parse_equipment(&ocr_result.text_blocks, page_type)
        .map_err(|e| format!("装备解析失败: {}", e))?;
    
    // 6. 装备评分
    let evaluator = EquipmentEvaluator::new()
        .map_err(|e| format!("评估器初始化失败: {}", e))?;
    let evaluation = evaluator.evaluate_normal(&equipment)
        .map_err(|e| format!("装备评分失败: {}", e))?;
    
    Ok(EquipmentRecognitionResult {
        equipment,
        evaluation,
        page_recognition: recognition_result,
    })
}

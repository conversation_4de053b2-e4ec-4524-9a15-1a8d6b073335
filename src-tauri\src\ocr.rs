use std::path::{Path, PathBuf};
use std::process::Stdio;
use std::time::Duration;
use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use tokio::process::Command;
use tokio::time::timeout;

use crate::config::ConfigService;

/// OCR文本块
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OcrTextBlock {
    /// 文本内容
    pub text: String,
    /// 置信度 (0.0-1.0)
    pub confidence: f32,
    /// 边界框坐标 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
    pub bbox: Vec<Vec<f32>>,
}

/// OCR识别结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OcrResult {
    /// OCR识别的文本块列表
    pub text_blocks: Vec<OcrTextBlock>,
    /// 处理状态
    pub status: String,
}

/// OCR场景类型
#[derive(Debug, <PERSON><PERSON>)]
pub enum OcrScenario {
    /// 页面识别场景
    PageDetection,
    /// 装备识别场景
    EquipmentDetection,
}

/// OCR服务
pub struct OcrService {
    config: ConfigService,
    paddleocr_path: PathBuf,
}

impl OcrService {
    /// 创建新的OCR服务
    pub fn new() -> Result<Self> {
        let config = ConfigService::new("config.json".to_string());
        let paddleocr_path = PathBuf::from("src-tauri/PaddleOCR-json/PaddleOCR-json.exe");
        
        Ok(Self {
            config,
            paddleocr_path,
        })
    }

    /// 使用指定配置创建OCR服务
    pub fn with_config(config: ConfigService) -> Self {
        let paddleocr_path = PathBuf::from("src-tauri/PaddleOCR-json/PaddleOCR-json.exe");
        
        Self {
            config,
            paddleocr_path,
        }
    }

    /// 设置PaddleOCR可执行文件路径
    pub fn set_paddleocr_path<P: AsRef<Path>>(&mut self, path: P) {
        self.paddleocr_path = path.as_ref().to_path_buf();
    }

    /// 执行OCR识别
    pub async fn recognize_text(&self, image_path: &Path, scenario: OcrScenario) -> Result<OcrResult> {
        // 验证图像文件存在
        if !image_path.exists() {
            return Err(anyhow::anyhow!("图像文件不存在: {:?}", image_path));
        }

        // 验证PaddleOCR可执行文件存在
        if !self.paddleocr_path.exists() {
            return Err(anyhow::anyhow!("PaddleOCR可执行文件不存在: {:?}", self.paddleocr_path));
        }

        // 构建命令行参数
        let mut cmd = Command::new(&self.paddleocr_path);
        cmd.arg("--image_path")
           .arg(image_path.to_string_lossy().to_string())
           .arg("--output_format")
           .arg("json");

        // 根据场景添加特定参数
        match scenario {
            OcrScenario::PageDetection => {
                cmd.arg("--det_limit_type")
                   .arg("max")
                   .arg("--det_limit_side_len")
                   .arg("96");
            }
            OcrScenario::EquipmentDetection => {
                let config = self.config.load_config()?;
                let equipment_settings = &config.ocr_settings.equipment_detection;
                
                cmd.arg("--det_thresh")
                   .arg(equipment_settings.detection_threshold.to_string())
                   .arg("--det_box_thresh")
                   .arg(equipment_settings.border_threshold.to_string())
                   .arg("--rec_thresh")
                   .arg(equipment_settings.recognition_threshold.to_string());
            }
        }

        // 设置标准输出和错误输出
        cmd.stdout(Stdio::piped())
           .stderr(Stdio::piped());

        // 执行命令并设置超时
        let output = timeout(Duration::from_millis(500), cmd.output())
            .await
            .context("OCR识别超时")?
            .context("执行OCR命令失败")?;

        // 检查命令执行状态
        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(anyhow::anyhow!("OCR识别失败: {}", stderr));
        }

        // 解析JSON输出
        let stdout = String::from_utf8_lossy(&output.stdout);
        let ocr_result = self.parse_paddleocr_output(&stdout)?;

        Ok(OcrResult {
            text_blocks: ocr_result,
            status: "success".to_string(),
        })
    }

    /// 解析PaddleOCR的JSON输出
    fn parse_paddleocr_output(&self, json_str: &str) -> Result<Vec<OcrTextBlock>> {
        // PaddleOCR-json的输出格式通常是一个数组，每个元素包含文本和坐标信息
        let parsed: serde_json::Value = serde_json::from_str(json_str)
            .context("解析OCR JSON输出失败")?;

        let mut text_blocks = Vec::new();

        if let Some(results) = parsed.as_array() {
            for result in results {
                if let Some(text_info) = result.as_array() {
                    if text_info.len() >= 2 {
                        // 解析坐标信息 (第一个元素)
                        let bbox = self.parse_bbox(&text_info[0])?;
                        
                        // 解析文本信息 (第二个元素)
                        if let Some(text_data) = text_info[1].as_array() {
                            if text_data.len() >= 2 {
                                let text = text_data[0].as_str().unwrap_or("").to_string();
                                let confidence = text_data[1].as_f64().unwrap_or(0.0) as f32;
                                
                                text_blocks.push(OcrTextBlock {
                                    text,
                                    confidence,
                                    bbox,
                                });
                            }
                        }
                    }
                }
            }
        }

        Ok(text_blocks)
    }

    /// 解析边界框坐标
    fn parse_bbox(&self, bbox_value: &serde_json::Value) -> Result<Vec<Vec<f32>>> {
        let mut bbox = Vec::new();
        
        if let Some(coords) = bbox_value.as_array() {
            for coord in coords {
                if let Some(point) = coord.as_array() {
                    if point.len() >= 2 {
                        let x = point[0].as_f64().unwrap_or(0.0) as f32;
                        let y = point[1].as_f64().unwrap_or(0.0) as f32;
                        bbox.push(vec![x, y]);
                    }
                }
            }
        }

        if bbox.len() != 4 {
            return Err(anyhow::anyhow!("无效的边界框坐标格式"));
        }

        Ok(bbox)
    }

    /// 提取所有识别到的文本内容
    pub fn extract_text_content(&self, ocr_result: &OcrResult) -> Vec<String> {
        ocr_result.text_blocks
            .iter()
            .map(|block| block.text.clone())
            .collect()
    }

    /// 根据置信度过滤文本块
    pub fn filter_by_confidence(&self, ocr_result: &OcrResult, min_confidence: f32) -> Vec<OcrTextBlock> {
        ocr_result.text_blocks
            .iter()
            .filter(|block| block.confidence >= min_confidence)
            .cloned()
            .collect()
    }

    /// 根据坐标区域过滤文本块
    pub fn filter_by_region(&self, ocr_result: &OcrResult, x_min: f32, y_min: f32, x_max: f32, y_max: f32) -> Vec<OcrTextBlock> {
        ocr_result.text_blocks
            .iter()
            .filter(|block| {
                // 检查文本块的中心点是否在指定区域内
                let center_x = block.bbox.iter().map(|p| p[0]).sum::<f32>() / 4.0;
                let center_y = block.bbox.iter().map(|p| p[1]).sum::<f32>() / 4.0;
                center_x >= x_min && center_x <= x_max && center_y >= y_min && center_y <= y_max
            })
            .cloned()
            .collect()
    }
}

/// Tauri命令接口

/// 执行OCR识别 - 页面检测场景
#[tauri::command]
pub async fn ocr_recognize_page(image_path: String) -> Result<OcrResult, String> {
    let ocr_service = OcrService::new()
        .map_err(|e| e.to_string())?;

    let path = Path::new(&image_path);
    ocr_service.recognize_text(path, OcrScenario::PageDetection).await
        .map_err(|e| e.to_string())
}

/// 执行OCR识别 - 装备检测场景
#[tauri::command]
pub async fn ocr_recognize_equipment(image_path: String) -> Result<OcrResult, String> {
    let ocr_service = OcrService::new()
        .map_err(|e| e.to_string())?;

    let path = Path::new(&image_path);
    ocr_service.recognize_text(path, OcrScenario::EquipmentDetection).await
        .map_err(|e| e.to_string())
}

/// 通用OCR识别接口
#[tauri::command]
pub async fn ocr_recognize_text(image_path: String, scenario: String) -> Result<OcrResult, String> {
    let ocr_service = OcrService::new()
        .map_err(|e| e.to_string())?;

    let scenario_type = match scenario.as_str() {
        "page" => OcrScenario::PageDetection,
        "equipment" => OcrScenario::EquipmentDetection,
        _ => return Err("无效的OCR场景类型".to_string()),
    };

    let path = Path::new(&image_path);
    ocr_service.recognize_text(path, scenario_type).await
        .map_err(|e| e.to_string())
}



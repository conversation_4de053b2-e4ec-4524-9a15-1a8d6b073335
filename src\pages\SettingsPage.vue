<template>
  <div style="padding: 1rem; display: flex; flex-direction: column; gap: 1rem; height: 100%;">
    <!-- 页面头部 -->
    <header style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 1rem; border-bottom: 1px solid #ddd;">
      <h1 style="margin: 0; font-size: 1.5rem;">系统设置</h1>
      <div style="display: flex; align-items: center; gap: 1rem;">
        <button
          @click="saveConfig"
          :disabled="isSaving"
          style="padding: 0.5rem 1rem; background: #007bff; color: white; border: none; border-radius: 4px;"
        >
          💾 {{ isSaving ? '保存中...' : '保存配置' }}
        </button>
        <div style="display: flex; align-items: center; gap: 0.5rem;">
          <span style="width: 8px; height: 8px; border-radius: 50%;" :style="{ background: isInitialized ? '#28a745' : '#ffc107' }"></span>
          <span style="font-size: 0.875rem;">{{ isInitialized ? '初始化完成' : '初始化中...' }}</span>
        </div>
        <span v-if="saveSuccess" style="color: #28a745; font-size: 0.875rem;">✓ 已保存</span>
        <span v-if="saveError" style="color: #dc3545; font-size: 0.875rem;">✗ 保存失败</span>
      </div>
    </header>

    <!-- 设置内容区域 -->
    <main style="flex: 1; overflow-y: auto; display: flex; flex-direction: column; gap: 1rem;">
      <!-- ADB连接设置 -->
      <fieldset>
        <legend><strong>ADB连接设置</strong></legend>

        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 0.5rem; border-bottom: 1px solid #ddd; width: 120px;">连接端口:</td>
            <td style="padding: 0.5rem; border-bottom: 1px solid #ddd;">
              <input
                v-model.number="config.adb_settings.port"
                type="number"
                placeholder="16384"
                style="width: 100px; padding: 0.25rem; border: 1px solid #ccc; border-radius: 4px;"
              />
            </td>
          </tr>
          <tr>
            <td style="padding: 0.5rem; border-bottom: 1px solid #ddd;">连接超时:</td>
            <td style="padding: 0.5rem; border-bottom: 1px solid #ddd;">
              <input
                v-model.number="config.adb_settings.timeout"
                type="number"
                placeholder="5000"
                style="width: 100px; padding: 0.25rem; border: 1px solid #ccc; border-radius: 4px;"
              /> 毫秒
            </td>
          </tr>
          <tr>
            <td style="padding: 0.5rem; border-bottom: 1px solid #ddd;">自动连接:</td>
            <td style="padding: 0.5rem; border-bottom: 1px solid #ddd;">
              <input
                v-model="config.adb_settings.auto_connect"
                type="checkbox"
              />
            </td>
          </tr>
          <tr>
            <td style="padding: 0.5rem;">连接测试:</td>
            <td style="padding: 0.5rem; display: flex; align-items: center; gap: 1rem;">
              <button
                @click="testAdbConnection"
                :disabled="isTestingAdb"
                style="padding: 0.25rem 0.75rem; background: #28a745; color: white; border: none; border-radius: 4px;"
              >
                ✓ {{ isTestingAdb ? '测试中...' : '连接测试' }}
              </button>
              <span v-if="adbTestResult" :style="{ color: adbTestResult.success ? '#28a745' : '#dc3545' }">
                {{ adbTestResult.success ? '✓ 已连接' : '✗ 连接失败' }}
              </span>
            </td>
          </tr>
        </table>
      </fieldset>

      <!-- 热键设置 -->
      <fieldset>
        <legend><strong>热键设置</strong></legend>

        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 0.5rem; border-bottom: 1px solid #ddd; width: 120px;">启用热键:</td>
            <td style="padding: 0.5rem; border-bottom: 1px solid #ddd;">
              <input
                v-model="config.hotkey_settings.enabled"
                type="checkbox"
              />
            </td>
          </tr>
          <tr>
            <td style="padding: 0.5rem;">热键组合:</td>
            <td style="padding: 0.5rem;">
              <input
                v-model="config.hotkey_settings.key"
                type="text"
                placeholder="Ctrl+Shift+E"
                style="width: 200px; padding: 0.25rem; border: 1px solid #ccc; border-radius: 4px;"
              />
            </td>
          </tr>
        </table>
      </fieldset>

      <!-- OCR设置 -->
      <fieldset>
        <legend><strong>OCR识别设置</strong></legend>

        <h4>页面识别参数</h4>
        <table style="width: 100%; border-collapse: collapse; margin-bottom: 1rem;">
          <tr>
            <td style="padding: 0.5rem; border-bottom: 1px solid #ddd; width: 120px;">边界阈值:</td>
            <td style="padding: 0.5rem; border-bottom: 1px solid #ddd;">
              <input
                v-model.number="config.ocr_settings.page_detection.border_threshold"
                type="number"
                step="0.1"
                style="width: 100px; padding: 0.25rem; border: 1px solid #ccc; border-radius: 4px;"
              />
            </td>
          </tr>
          <tr>
            <td style="padding: 0.5rem;">识别阈值:</td>
            <td style="padding: 0.5rem;">
              <input
                v-model.number="config.ocr_settings.page_detection.recognition_threshold"
                type="number"
                step="0.1"
                style="width: 100px; padding: 0.25rem; border: 1px solid #ccc; border-radius: 4px;"
              />
            </td>
          </tr>
        </table>

        <h4>装备识别参数</h4>
        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 0.5rem; border-bottom: 1px solid #ddd; width: 120px;">检测阈值:</td>
            <td style="padding: 0.5rem; border-bottom: 1px solid #ddd;">
              <input
                v-model.number="config.ocr_settings.equipment_detection.detection_threshold"
                type="number"
                step="0.1"
                style="width: 100px; padding: 0.25rem; border: 1px solid #ccc; border-radius: 4px;"
              />
            </td>
          </tr>
          <tr>
            <td style="padding: 0.5rem; border-bottom: 1px solid #ddd;">边界阈值:</td>
            <td style="padding: 0.5rem; border-bottom: 1px solid #ddd;">
              <input
                v-model.number="config.ocr_settings.equipment_detection.border_threshold"
                type="number"
                step="0.1"
                style="width: 100px; padding: 0.25rem; border: 1px solid #ccc; border-radius: 4px;"
              />
            </td>
          </tr>
          <tr>
            <td style="padding: 0.5rem;">识别阈值:</td>
            <td style="padding: 0.5rem;">
              <input
                v-model.number="config.ocr_settings.equipment_detection.recognition_threshold"
                type="number"
                step="0.1"
                style="width: 100px; padding: 0.25rem; border: 1px solid #ccc; border-radius: 4px;"
              />
            </td>
          </tr>
        </table>
      </fieldset>

      <!-- 装备分数标准设置 -->
      <fieldset>
        <legend><strong>装备分数标准</strong></legend>

        <table style="width: 100%; border-collapse: collapse; margin-bottom: 1rem;">
          <thead>
            <tr style="background: #f8f9fa;">
              <th style="padding: 0.5rem; border: 1px solid #ddd; text-align: left;">装备类型</th>
              <th style="padding: 0.5rem; border: 1px solid #ddd;">+0-2</th>
              <th style="padding: 0.5rem; border: 1px solid #ddd;">+3-5</th>
              <th style="padding: 0.5rem; border: 1px solid #ddd;">+6-8</th>
              <th style="padding: 0.5rem; border: 1px solid #ddd;">+9-11</th>
              <th style="padding: 0.5rem; border: 1px solid #ddd;">+12-14</th>
              <th style="padding: 0.5rem; border: 1px solid #ddd;">+15</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style="padding: 0.5rem; border: 1px solid #ddd; font-weight: bold;">左三装备</td>
              <td v-for="(_, index) in config.scoring_standards.left_side_equipment" :key="'left-' + index" style="padding: 0.5rem; border: 1px solid #ddd;">
                <input
                  v-model.number="config.scoring_standards.left_side_equipment[index]"
                  type="number"
                  style="width: 60px; padding: 0.25rem; border: 1px solid #ccc; border-radius: 4px; text-align: center;"
                />
              </td>
            </tr>
            <tr>
              <td style="padding: 0.5rem; border: 1px solid #ddd; font-weight: bold;">右三装备</td>
              <td v-for="(_, index) in config.scoring_standards.right_side_equipment" :key="'right-' + index" style="padding: 0.5rem; border: 1px solid #ddd;">
                <input
                  v-model.number="config.scoring_standards.right_side_equipment[index]"
                  type="number"
                  style="width: 60px; padding: 0.25rem; border: 1px solid #ccc; border-radius: 4px; text-align: center;"
                />
              </td>
            </tr>
            <tr>
              <td style="padding: 0.5rem; border: 1px solid #ddd; font-weight: bold;">速度装备</td>
              <td v-for="(_, index) in config.scoring_standards.speed_equipment" :key="'speed-' + index" style="padding: 0.5rem; border: 1px solid #ddd;">
                <input
                  v-model.number="config.scoring_standards.speed_equipment[index]"
                  type="number"
                  style="width: 60px; padding: 0.25rem; border: 1px solid #ccc; border-radius: 4px; text-align: center;"
                />
              </td>
            </tr>
          </tbody>
        </table>

        <h4>重铸界面分数标准</h4>
        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 0.5rem; border-bottom: 1px solid #ddd; width: 120px;">左三装备:</td>
            <td style="padding: 0.5rem; border-bottom: 1px solid #ddd;">
              <input
                v-model.number="config.scoring_standards.recast_standards.left_side"
                type="number"
                style="width: 100px; padding: 0.25rem; border: 1px solid #ccc; border-radius: 4px;"
              />
            </td>
          </tr>
          <tr>
            <td style="padding: 0.5rem;">右三装备:</td>
            <td style="padding: 0.5rem;">
              <input
                v-model.number="config.scoring_standards.recast_standards.right_side"
                type="number"
                style="width: 100px; padding: 0.25rem; border: 1px solid #ccc; border-radius: 4px;"
              />
            </td>
          </tr>
        </table>
      </fieldset>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { invoke } from '@tauri-apps/api/core'

// 配置数据结构
interface Config {
  adb_settings: {
    port: number
    timeout: number
    auto_connect: boolean
  }
  hotkey_settings: {
    enabled: boolean
    key: string
  }
  ocr_settings: {
    page_detection: {
      border_threshold: number
      recognition_threshold: number
    }
    equipment_detection: {
      detection_threshold: number
      border_threshold: number
      recognition_threshold: number
    }
  }
  scoring_standards: {
    left_side_equipment: number[]
    right_side_equipment: number[]
    speed_equipment: number[]
    recast_standards: {
      left_side: number
      right_side: number
    }
  }
}

// 默认配置
const defaultConfig: Config = {
  adb_settings: {
    port: 16384,
    timeout: 5000,
    auto_connect: true
  },
  hotkey_settings: {
    enabled: true,
    key: 'F1'
  },
  ocr_settings: {
    page_detection: {
      border_threshold: 0.8,
      recognition_threshold: 0.7
    },
    equipment_detection: {
      detection_threshold: 0.6,
      border_threshold: 0.8,
      recognition_threshold: 0.7
    }
  },
  scoring_standards: {
    left_side_equipment: [21, 28, 35, 42, 49, 55],
    right_side_equipment: [19, 26, 33, 40, 47, 53],
    speed_equipment: [3, 6, 10, 12, 12, 14],
    recast_standards: {
      left_side: 67,
      right_side: 65
    }
  }
}

// 响应式数据
const config = ref<Config>({ ...defaultConfig })
const isSaving = ref(false)
const saveSuccess = ref(false)
const saveError = ref(false)
const isTestingAdb = ref(false)
const adbTestResult = ref<{ success: boolean; message: string } | null>(null)
const isInitialized = ref(false)

// 加载配置
const loadConfig = async () => {
  try {
    const loadedConfig = await invoke<Config>('load_config')
    config.value = { ...defaultConfig, ...loadedConfig }
  } catch (error) {
    console.error('加载配置失败:', error)
    // 使用默认配置
    config.value = { ...defaultConfig }
  }
}

// 保存配置
const saveConfig = async () => {
  isSaving.value = true
  saveSuccess.value = false
  saveError.value = false
  
  try {
    await invoke('save_config', { config: config.value })
    saveSuccess.value = true
    setTimeout(() => {
      saveSuccess.value = false
    }, 3000)
  } catch (error) {
    console.error('保存配置失败:', error)
    saveError.value = true
    setTimeout(() => {
      saveError.value = false
    }, 3000)
  } finally {
    isSaving.value = false
  }
}

// 测试ADB连接
const testAdbConnection = async () => {
  isTestingAdb.value = true
  adbTestResult.value = null
  
  try {
    const result = await invoke<boolean>('adb_check_connection')
    adbTestResult.value = {
      success: result,
      message: result ? '连接成功' : '连接失败'
    }
  } catch (error) {
    adbTestResult.value = {
      success: false,
      message: `连接错误: ${error}`
    }
  } finally {
    isTestingAdb.value = false
    setTimeout(() => {
      adbTestResult.value = null
    }, 5000)
  }
}

// 组件挂载时加载配置
onMounted(async () => {
  try {
    await loadConfig()
    isInitialized.value = true
  } catch (error) {
    console.error('初始化失败:', error)
    isInitialized.value = true // 即使失败也标记为已初始化，使用默认配置
  }
})
</script>


<template>
  <main :class="['settings-container']">
    <!-- 页面头部 -->
    <header :class="['settings-header']">
      <h1 :class="['page-title']">系统设置</h1>
      <div :class="['header-actions']">
        <button 
          @click="saveConfig" 
          :disabled="isSaving"
          :class="['btn', 'btn-primary', { 'btn-disabled': isSaving }]"
        >
          {{ isSaving ? '保存中...' : '保存配置' }}
        </button>
        <span v-if="saveSuccess" :class="['status-message', 'success']">✓ 已保存</span>
        <span v-if="saveError" :class="['status-message', 'error']">✗ 保存失败</span>
      </div>
    </header>

    <!-- 设置表单 -->
    <form :class="['settings-form']" @submit.prevent="saveConfig">
      <!-- ADB连接设置 -->
      <fieldset :class="['settings-section']">
        <legend :class="['section-legend']">ADB连接设置</legend>
        
        <div :class="['form-fields']">
          <div :class="['form-row']">
            <label :class="['form-label']">连接端口</label>
            <input 
              v-model.number="config.adb_settings.port" 
              type="number" 
              placeholder="16384"
              :class="['form-input']"
            />
          </div>
          
          <div :class="['form-row']">
            <label :class="['form-label']">连接超时 (毫秒)</label>
            <input 
              v-model.number="config.adb_settings.timeout" 
              type="number" 
              placeholder="5000"
              :class="['form-input']"
            />
          </div>
          
          <div :class="['form-row']">
            <label :class="['form-label']">自动连接</label>
            <input 
              v-model="config.adb_settings.auto_connect" 
              type="checkbox"
              :class="['form-checkbox']"
            />
          </div>
          
          <div :class="['form-row']">
            <button 
              type="button"
              @click="testAdbConnection" 
              :disabled="isTestingAdb"
              :class="['btn', 'btn-success', { 'btn-disabled': isTestingAdb }]"
            >
              {{ isTestingAdb ? '测试中...' : '测试连接' }}
            </button>
            <span v-if="adbTestResult" :class="['test-result', { 'success': adbTestResult.success, 'error': !adbTestResult.success }]">
              {{ adbTestResult.message }}
            </span>
          </div>
        </div>
      </fieldset>

      <!-- 热键设置 -->
      <fieldset :class="['settings-section']">
        <legend :class="['section-legend']">热键设置</legend>
        
        <div :class="['form-fields']">
          <div :class="['form-row']">
            <label :class="['form-label']">启用热键</label>
            <input 
              v-model="config.hotkey_settings.enabled" 
              type="checkbox"
              :class="['form-checkbox']"
            />
          </div>
          
          <div :class="['form-row']">
            <label :class="['form-label']">快捷键</label>
            <input 
              v-model="config.hotkey_settings.key" 
              type="text" 
              placeholder="F1"
              :disabled="!config.hotkey_settings.enabled"
              :class="['form-input', { 'disabled': !config.hotkey_settings.enabled }]"
            />
          </div>
        </div>
      </fieldset>

      <!-- OCR识别设置 -->
      <fieldset :class="['settings-section']">
        <legend :class="['section-legend']">OCR识别设置</legend>
        
        <div :class="['form-fields']">
          <h3 :class="['subsection-title']">页面识别参数</h3>
          
          <div :class="['form-row']">
            <label :class="['form-label']">边界阈值</label>
            <input 
              v-model.number="config.ocr_settings.page_detection.border_threshold" 
              type="number" 
              step="0.1" 
              min="0" 
              max="1" 
              :class="['form-input']"
            />
          </div>
          
          <div :class="['form-row']">
            <label :class="['form-label']">识别阈值</label>
            <input 
              v-model.number="config.ocr_settings.page_detection.recognition_threshold" 
              type="number" 
              step="0.1" 
              min="0" 
              max="1" 
              :class="['form-input']"
            />
          </div>
          
          <h3 :class="['subsection-title']">装备识别参数</h3>
          
          <div :class="['form-row']">
            <label :class="['form-label']">检测阈值</label>
            <input 
              v-model.number="config.ocr_settings.equipment_detection.detection_threshold" 
              type="number" 
              step="0.1" 
              min="0" 
              max="1" 
              :class="['form-input']"
            />
          </div>
          
          <div :class="['form-row']">
            <label :class="['form-label']">边界阈值</label>
            <input 
              v-model.number="config.ocr_settings.equipment_detection.border_threshold" 
              type="number" 
              step="0.1" 
              min="0" 
              max="1" 
              :class="['form-input']"
            />
          </div>
          
          <div :class="['form-row']">
            <label :class="['form-label']">识别阈值</label>
            <input 
              v-model.number="config.ocr_settings.equipment_detection.recognition_threshold" 
              type="number" 
              step="0.1" 
              min="0" 
              max="1" 
              :class="['form-input']"
            />
          </div>
        </div>
      </fieldset>

      <!-- 装备分数标准设置 -->
      <fieldset :class="['settings-section']">
        <legend :class="['section-legend']">装备分数标准</legend>
        
        <div :class="['form-fields']">
          <h3 :class="['subsection-title']">左三装备分数标准</h3>
          <div :class="['score-grid']">
            <div v-for="(score, index) in config.scoring_standards.left_side_equipment" :key="'left-' + index" :class="['score-item']">
              <label :class="['score-label']">{{ index + 1 }}星</label>
              <input 
                v-model.number="config.scoring_standards.left_side_equipment[index]" 
                type="number" 
                :class="['score-input']"
              />
            </div>
          </div>
          
          <h3 :class="['subsection-title']">右三装备分数标准</h3>
          <div :class="['score-grid']">
            <div v-for="(score, index) in config.scoring_standards.right_side_equipment" :key="'right-' + index" :class="['score-item']">
              <label :class="['score-label']">{{ index + 1 }}星</label>
              <input 
                v-model.number="config.scoring_standards.right_side_equipment[index]" 
                type="number" 
                :class="['score-input']"
              />
            </div>
          </div>
          
          <h3 :class="['subsection-title']">速度装备分数标准</h3>
          <div :class="['score-grid']">
            <div v-for="(score, index) in config.scoring_standards.speed_equipment" :key="'speed-' + index" :class="['score-item']">
              <label :class="['score-label']">{{ index + 1 }}星</label>
              <input 
                v-model.number="config.scoring_standards.speed_equipment[index]" 
                type="number" 
                :class="['score-input']"
              />
            </div>
          </div>
          
          <h3 :class="['subsection-title']">重铸标准</h3>
          <div :class="['form-row']">
            <label :class="['form-label']">左三装备重铸标准</label>
            <input 
              v-model.number="config.scoring_standards.recast_standards.left_side" 
              type="number" 
              :class="['form-input']"
            />
          </div>
          <div :class="['form-row']">
            <label :class="['form-label']">右三装备重铸标准</label>
            <input 
              v-model.number="config.scoring_standards.recast_standards.right_side" 
              type="number" 
              :class="['form-input']"
            />
          </div>
        </div>
      </fieldset>
    </form>
  </main>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { invoke } from '@tauri-apps/api/core'

// 配置数据结构
interface Config {
  adb_settings: {
    port: number
    timeout: number
    auto_connect: boolean
  }
  hotkey_settings: {
    enabled: boolean
    key: string
  }
  ocr_settings: {
    page_detection: {
      border_threshold: number
      recognition_threshold: number
    }
    equipment_detection: {
      detection_threshold: number
      border_threshold: number
      recognition_threshold: number
    }
  }
  scoring_standards: {
    left_side_equipment: number[]
    right_side_equipment: number[]
    speed_equipment: number[]
    recast_standards: {
      left_side: number
      right_side: number
    }
  }
}

// 默认配置
const defaultConfig: Config = {
  adb_settings: {
    port: 16384,
    timeout: 5000,
    auto_connect: true
  },
  hotkey_settings: {
    enabled: true,
    key: 'F1'
  },
  ocr_settings: {
    page_detection: {
      border_threshold: 0.8,
      recognition_threshold: 0.7
    },
    equipment_detection: {
      detection_threshold: 0.6,
      border_threshold: 0.8,
      recognition_threshold: 0.7
    }
  },
  scoring_standards: {
    left_side_equipment: [21, 28, 35, 42, 49, 55],
    right_side_equipment: [19, 26, 33, 40, 47, 53],
    speed_equipment: [3, 6, 10, 12, 12, 14],
    recast_standards: {
      left_side: 67,
      right_side: 65
    }
  }
}

// 响应式数据
const config = ref<Config>({ ...defaultConfig })
const isSaving = ref(false)
const saveSuccess = ref(false)
const saveError = ref(false)
const isTestingAdb = ref(false)
const adbTestResult = ref<{ success: boolean; message: string } | null>(null)

// 加载配置
const loadConfig = async () => {
  try {
    const loadedConfig = await invoke<Config>('load_config')
    config.value = { ...defaultConfig, ...loadedConfig }
  } catch (error) {
    console.error('加载配置失败:', error)
    // 使用默认配置
    config.value = { ...defaultConfig }
  }
}

// 保存配置
const saveConfig = async () => {
  isSaving.value = true
  saveSuccess.value = false
  saveError.value = false
  
  try {
    await invoke('save_config', { config: config.value })
    saveSuccess.value = true
    setTimeout(() => {
      saveSuccess.value = false
    }, 3000)
  } catch (error) {
    console.error('保存配置失败:', error)
    saveError.value = true
    setTimeout(() => {
      saveError.value = false
    }, 3000)
  } finally {
    isSaving.value = false
  }
}

// 测试ADB连接
const testAdbConnection = async () => {
  isTestingAdb.value = true
  adbTestResult.value = null
  
  try {
    const result = await invoke<boolean>('adb_check_connection')
    adbTestResult.value = {
      success: result,
      message: result ? '连接成功' : '连接失败'
    }
  } catch (error) {
    adbTestResult.value = {
      success: false,
      message: `连接错误: ${error}`
    }
  } finally {
    isTestingAdb.value = false
    setTimeout(() => {
      adbTestResult.value = null
    }, 5000)
  }
}

// 组件挂载时加载配置
onMounted(() => {
  loadConfig()
})
</script>

<style scoped>
.settings-container {
  height: 100%;
  padding: 24px;
  background: #f8f9fa;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.settings-section {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
}

.section-legend {
  padding: 0 8px;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.form-label {
  min-width: 120px;
  font-size: 14px;
  color: #495057;
}

.form-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.form-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-input.disabled {
  background: #f8f9fa;
  color: #6c757d;
}

.form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.form-range {
  width: 100%;
}

.form-checkbox {
  width: auto;
  flex: none;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #495057;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  margin-right: 8px;
  transition: background-color 0.2s;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover:not(.btn-disabled) {
  background: #1e7e34;
}

.btn-primary {
  background: #007bff;
  color: white;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
}

.btn-primary:hover:not(.btn-disabled) {
  background: #0056b3;
}

.btn-disabled {
  background: #6c757d !important;
  cursor: not-allowed;
}

.test-result {
  font-size: 14px;
}

.test-result.success {
  color: #28a745;
}

.test-result.error {
  color: #dc3545;
}

.status-message {
  font-size: 14px;
}

.status-message.success {
  color: #28a745;
}

.status-message.error {
  color: #dc3545;
}

.subsection-title {
  margin: 16px 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #6c757d;
}

.score-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
  margin-top: 8px;
}

.score-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-label {
  font-size: 12px;
  color: #6c757d;
  min-width: 40px;
}

.score-input {
  width: 60px;
  padding: 4px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
}
</style>
<template>
  <div style="display: flex; gap: 1rem; padding: 1rem; height: 100%;">
    <!-- 左侧列：控制区域、分数显示、装备信息 -->
    <div style="flex: 1; display: flex; flex-direction: column; gap: 1rem;">
      <!-- 控制区域 -->
      <fieldset>
        <legend><strong>装备识别助手</strong></legend>
        <button
          @click="handleRecognize"
          :disabled="isLoading"
          style="width: 100%; padding: 0.75rem; background: #007bff; color: white; border: none; border-radius: 4px; font-size: 1rem;"
        >
          📷 {{ isLoading ? '识别中...' : '开始识别' }}
        </button>
      </fieldset>

      <!-- 分数显示区域 -->
      <fieldset v-if="recognitionResult?.evaluation">
        <legend>装备分数</legend>
        <div style="text-align: center; margin: 1rem 0;">
          <div
            style="font-size: 3rem; font-weight: bold; margin-bottom: 0.5rem;"
            :style="{ color: isScorePass ? '#28a745' : '#dc3545' }"
          >
            {{ recognitionResult.evaluation.total_score }}
          </div>
          <div v-if="recognitionResult.evaluation.enhancement_recommendation">
            强化建议: {{ recognitionResult.evaluation.enhancement_recommendation }}
          </div>
        </div>
      </fieldset>

      <!-- 装备信息区域 -->
      <fieldset v-if="recognitionResult?.equipment">
        <legend>装备信息</legend>
        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 0.25rem; border-bottom: 1px solid #ddd;">稀有度:</td>
            <td style="padding: 0.25rem; border-bottom: 1px solid #ddd; font-weight: bold;">{{ recognitionResult.equipment.rarity }}</td>
          </tr>
          <tr>
            <td style="padding: 0.25rem; border-bottom: 1px solid #ddd;">类型:</td>
            <td style="padding: 0.25rem; border-bottom: 1px solid #ddd;">{{ recognitionResult.equipment.type }}</td>
          </tr>
          <tr>
            <td style="padding: 0.25rem; border-bottom: 1px solid #ddd;">强化:</td>
            <td style="padding: 0.25rem; border-bottom: 1px solid #ddd;">+{{ recognitionResult.equipment.enhance_level }}</td>
          </tr>
          <tr v-if="recognitionResult.equipment.set_type">
            <td style="padding: 0.25rem; border-bottom: 1px solid #ddd;">套装:</td>
            <td style="padding: 0.25rem; border-bottom: 1px solid #ddd;">{{ recognitionResult.equipment.set_type }}</td>
          </tr>
        </table>

        <div v-if="recognitionResult.equipment.main_stat" style="margin-top: 1rem;">
          <h4>主属性</h4>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 0.25rem; border-bottom: 1px solid #ddd;">{{ recognitionResult.equipment.main_stat.type }}</td>
              <td style="padding: 0.25rem; border-bottom: 1px solid #ddd; text-align: right;">{{ formatStatValue(recognitionResult.equipment.main_stat) }}</td>
            </tr>
          </table>
        </div>

        <div v-if="recognitionResult.equipment.sub_stats?.length" style="margin-top: 1rem;">
          <h4>副属性</h4>
          <table style="width: 100%; border-collapse: collapse;">
            <tr v-for="(stat, index) in recognitionResult.equipment.sub_stats" :key="index">
              <td style="padding: 0.25rem; border-bottom: 1px solid #ddd;">{{ stat.type }}</td>
              <td style="padding: 0.25rem; border-bottom: 1px solid #ddd; text-align: right;">{{ formatStatValue(stat) }}</td>
            </tr>
          </table>
        </div>
      </fieldset>
    </div>

    <!-- 右侧列：截图预览、运行日志 -->
    <div style="flex: 1; display: flex; flex-direction: column; gap: 1rem;">
      <!-- 截图预览区域 -->
      <fieldset style="flex: 1;">
        <legend>截图预览</legend>
        <div style="height: 100%; display: flex; align-items: center; justify-content: center; border: 2px dashed #ddd; border-radius: 4px;">
          <img
            v-if="screenshotUrl"
            :src="screenshotUrl"
            alt="装备截图"
            style="max-width: 100%; max-height: 100%; object-fit: contain;"
          />
          <div v-else style="color: #666;">暂无截图</div>
        </div>
      </fieldset>

      <!-- 运行日志区域 -->
      <fieldset style="flex: 1;">
        <legend>运行日志</legend>
        <div style="height: 100%; overflow-y: auto; font-family: monospace; font-size: 0.875rem; background: #f8f9fa; padding: 0.5rem; border-radius: 4px;">
          <div v-for="log in logs" :key="log.id" style="margin-bottom: 0.25rem;">
            <span style="color: #666;">[{{ formatTime(log.timestamp) }}]</span>
            <span :style="{ color: getLogColor(log.level) }">{{ log.message }}</span>
          </div>
          <div v-if="!logs.length" style="color: #666; text-align: center;">暂无日志</div>
        </div>
      </fieldset>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { invoke } from '@tauri-apps/api/core'
import type { EquipmentRecognitionResult } from '../types/equipment'

// 组件状态
const isLoading = ref(false)
const recognitionResult = ref<EquipmentRecognitionResult | null>(null)
const screenshotUrl = ref<string | null>(null)

// 日志系统
interface LogItem {
  id: number
  timestamp: Date
  level: 'info' | 'success' | 'error' | 'warning'
  message: string
}

const logs = reactive<LogItem[]>([])
let logIdCounter = 0

// 计算属性：判断分数是否达标
const isScorePass = computed(() => {
  if (!recognitionResult.value?.evaluation) return false
  return recognitionResult.value.evaluation.total_score >= 40
})

// 格式化属性值显示
const formatStatValue = (stat: { value: number; is_percent: boolean }) => {
  return stat.is_percent ? `${stat.value}%` : stat.value.toString()
}

// 添加日志
const addLog = (level: LogItem['level'], message: string) => {
  logs.unshift({
    id: ++logIdCounter,
    timestamp: new Date(),
    level,
    message
  })

  if (logs.length > 50) {
    logs.splice(50)
  }
}

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取日志颜色
const getLogColor = (level: string) => {
  switch (level) {
    case 'success': return '#28a745'
    case 'error': return '#dc3545'
    case 'warning': return '#ffc107'
    default: return '#6c757d'
  }
}

// 处理识别按钮点击
const handleRecognize = async () => {
  try {
    isLoading.value = true
    addLog('info', '开始装备识别...')

    const result = await invoke<EquipmentRecognitionResult>('equipment_recognize')

    recognitionResult.value = result
    // 从识别结果中获取截图路径（如果有的话）
    screenshotUrl.value = null
    addLog('success', '装备识别完成')

  } catch (error) {
    console.error('装备识别失败:', error)
    addLog('error', `识别失败: ${error instanceof Error ? error.message : '未知错误'}`)
  } finally {
    isLoading.value = false
  }
}
</script>


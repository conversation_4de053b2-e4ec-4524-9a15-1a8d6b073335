<template>
  <div class="equipment-page">
    <!-- 左侧列：控制区域、分数显示、装备信息 -->
    <div class="left-column">
      <!-- 控制区域 -->
      <div class="control-section">
        <h2 class="section-title">装备识别助手</h2>
        <button 
          class="recognize-btn" 
          @click="handleRecognize" 
          :disabled="isLoading"
        >
          {{ isLoading ? '识别中...' : '开始识别' }}
        </button>
      </div>

      <!-- 分数显示区域 -->
      <div class="score-section" v-if="recognitionResult?.evaluation">
        <h3 class="section-title">评分结果</h3>
        <div class="score-display">
          <div class="total-score">
            {{ recognitionResult.evaluation.total_score }}
          </div>
          <div class="score-label">总分</div>
        </div>
        <div class="enhancement-advice" v-if="recognitionResult.evaluation.enhancement_recommendation">
          <h4>强化建议</h4>
          <p>{{ recognitionResult.evaluation.enhancement_recommendation }}</p>
        </div>
      </div>

      <!-- 装备信息区域 -->
      <div class="equipment-info" v-if="recognitionResult?.equipment">
        <h3 class="section-title">装备信息</h3>
        <div class="basic-info">
          <span class="rarity">{{ recognitionResult.equipment.rarity }}</span>
          <span class="equipment-type">{{ recognitionResult.equipment.equipment_type }}</span>
          <span class="enhancement-level">+{{ recognitionResult.equipment.enhancement_level }}</span>
        </div>
        <div class="main-stat" v-if="recognitionResult.equipment.main_stat">
          <h4>主属性</h4>
          <div class="stat-item">
            <span class="stat-name">{{ recognitionResult.equipment.main_stat.stat_type }}</span>
            <span class="stat-value">{{ recognitionResult.equipment.main_stat.value }}</span>
          </div>
        </div>
        <div class="sub-stats" v-if="recognitionResult.equipment.sub_stats?.length">
          <h4>副属性</h4>
          <div 
            class="stat-item" 
            v-for="(stat, index) in recognitionResult.equipment.sub_stats" 
            :key="index"
          >
            <span class="stat-name">{{ stat.stat_type }}</span>
            <span class="stat-value">{{ stat.value }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧列：截图预览、运行日志 -->
    <div class="right-column">
      <!-- 截图预览区域 -->
      <div class="screenshot-section">
        <h3 class="section-title">截图预览</h3>
        <div class="screenshot-preview">
          <img 
            v-if="screenshotUrl" 
            :src="screenshotUrl" 
            alt="装备截图" 
            class="screenshot-image"
          />
          <div v-else class="screenshot-placeholder">
            <p>暂无截图</p>
          </div>
        </div>
      </div>

      <!-- 运行日志区域 -->
      <div class="log-section">
        <h3 class="section-title">运行日志</h3>
        <div class="log-container">
          <div 
            class="log-item" 
            v-for="log in logs" 
            :key="log.id"
            :class="`log-${log.level}`"
          >
            <span class="log-time">{{ formatTime(log.timestamp) }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
          <div v-if="!logs.length" class="log-placeholder">
            <p>暂无日志</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useEquipmentRecognition } from '../composables/useEquipmentRecognition'
import type { EquipmentRecognitionResult } from '../types/equipment'

// 使用装备识别组合式函数
const { recognizeEquipment } = useEquipmentRecognition()

// 组件状态
const isLoading = ref(false)
const recognitionResult = ref<EquipmentRecognitionResult | null>(null)
const screenshotUrl = ref<string | null>(null)

// 日志系统
interface LogItem {
  id: number
  timestamp: Date
  level: 'info' | 'success' | 'error' | 'warning'
  message: string
}

const logs = reactive<LogItem[]>([])
let logIdCounter = 0

// 添加日志
const addLog = (level: LogItem['level'], message: string) => {
  logs.unshift({
    id: ++logIdCounter,
    timestamp: new Date(),
    level,
    message
  })
  
  // 限制日志数量
  if (logs.length > 50) {
    logs.splice(50)
  }
}

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 处理识别按钮点击
const handleRecognize = async () => {
  try {
    isLoading.value = true
    addLog('info', '开始装备识别...')
    
    const result = await recognizeEquipment()
    
    recognitionResult.value = result
    addLog('success', '装备识别完成')
    
    // 模拟截图URL（实际项目中应该从识别结果中获取）
    screenshotUrl.value = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
    
  } catch (error) {
    console.error('装备识别失败:', error)
    addLog('error', `识别失败: ${error instanceof Error ? error.message : '未知错误'}`)
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.equipment-page {
  height: 100%;
  display: flex;
  gap: 24px;
  padding: 24px;
  background: #f8f9fa;
}

.left-column,
.right-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.control-section,
.score-section,
.equipment-info,
.screenshot-section,
.log-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.recognize-btn {
  width: 100%;
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.recognize-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.score-display {
  text-align: center;
  margin-bottom: 16px;
}

.total-score {
  font-size: 48px;
  font-weight: bold;
  color: #28a745;
  line-height: 1;
}

.score-label {
  font-size: 14px;
  color: #6c757d;
  margin-top: 4px;
}

.enhancement-advice {
  background: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 4px;
  padding: 12px;
}

.enhancement-advice h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #1976d2;
}

.enhancement-advice p {
  margin: 0;
  font-size: 13px;
  color: #424242;
  line-height: 1.4;
}

.basic-info {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.rarity {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background: #ffeaa7;
  color: #2d3436;
}

.equipment-type {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background: #74b9ff;
  color: white;
}

.enhancement-level {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background: #00b894;
  color: white;
}

.main-stat,
.sub-stats {
  margin-bottom: 12px;
}

.main-stat h4,
.sub-stats h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #495057;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #e9ecef;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-name {
  font-size: 13px;
  color: #495057;
}

.stat-value {
  font-size: 13px;
  font-weight: 500;
  color: #2c3e50;
}

.screenshot-preview {
  min-height: 200px;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.screenshot-image {
  max-width: 100%;
  max-height: 300px;
  border-radius: 4px;
}

.screenshot-placeholder {
  color: #6c757d;
  text-align: center;
}

.log-section {
  flex: 1;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px;
}

.log-item {
  display: flex;
  gap: 8px;
  padding: 4px 0;
  font-size: 12px;
  border-bottom: 1px solid #f8f9fa;
}

.log-time {
  color: #6c757d;
  font-family: monospace;
  white-space: nowrap;
}

.log-message {
  flex: 1;
}

.log-info .log-message {
  color: #495057;
}

.log-success .log-message {
  color: #28a745;
}

.log-error .log-message {
  color: #dc3545;
}

.log-warning .log-message {
  color: #f39c12;
}

.log-placeholder {
  color: #6c757d;
  text-align: center;
  padding: 20px;
}
</style>
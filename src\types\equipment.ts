/**
 * 装备相关类型定义
 * 基于后端Rust结构体定义，确保类型安全的API调用
 */

// 装备稀有度枚举
export enum Rarity {
  Legendary = '传说',
  Epic = '英雄',
  Rare = '稀有',
  Normal = '高级',
  Unknown = '未知'
}

// 装备类型枚举
export enum EquipmentType {
  Weapon = '武器',
  Helmet = '头盔',
  Armor = '铠甲',
  Necklace = '项链',
  Ring = '戒指',
  Boots = '鞋子',
  Unknown = '未知'
}

// 属性类型枚举
export enum StatType {
  Speed = '速度',
  Health = '生命值',
  Defense = '防御力',
  Attack = '攻击力',
  CriticalRate = '暴击率',
  CriticalDamage = '暴击伤害',
  EffectResistance = '效果抗性',
  EffectHit = '效果命中',
  Unknown = '未知'
}

// 装备属性接口
export interface Stat {
  /** 属性类型 */
  type: StatType
  /** 属性值 */
  value: number
  /** 是否为百分比 */
  is_percent: boolean
}

// 重铸属性接口
export interface RecastStat {
  /** 属性类型 */
  type: StatType
  /** 重铸前数值 */
  before_value: number
  /** 重铸后数值 */
  after_value: number
  /** 是否为百分比 */
  is_percent: boolean
}

// 装备信息接口
export interface Equipment {
  /** 稀有度 */
  rarity: Rarity
  /** 装备类型 */
  type: EquipmentType
  /** 强化等级 */
  enhance_level: number
  /** 主属性 */
  main_stat: Stat
  /** 副属性列表 */
  sub_stats: Stat[]
  /** 套装类型 */
  set_type: string
  /** 重铸属性（可选） */
  recast_stats?: RecastStat[]
}

// 评分详细分解接口
export interface ScoreBreakdown {
  /** 各属性得分详情 */
  details: Array<[string, number]>
}

// 重铸评估结果接口
export interface RecastEvaluation {
  /** 重铸前分数 */
  before_score: number
  /** 重铸后分数 */
  after_score: number
  /** 分数差值 */
  score_difference: number
  /** 重铸建议 */
  recommendation: string
}

// 装备评分结果接口
export interface ScoreResult {
  /** 总分数 */
  total_score: number
  /** 强化建议文本 */
  enhancement_recommendation: string
  /** 是否为速度装备 */
  is_speed_equipment: boolean
  /** 装备类型 */
  equipment_type: string
  /** 当前强化等级 */
  current_enhancement: number
  /** 分数详细分解 */
  score_breakdown: ScoreBreakdown
  /** 重铸评估结果（可选） */
  recast_evaluation?: RecastEvaluation
}

// 页面识别结果接口（从detection.rs导入）
export interface PageRecognitionResult {
  /** 页面类型 */
  page_type: string
  /** 识别置信度 */
  confidence: number
  /** 识别到的文本块 */
  detected_text: string[]
}

// 装备识别完整结果接口
export interface EquipmentRecognitionResult {
  /** 装备信息 */
  equipment: Equipment
  /** 评分结果 */
  evaluation: ScoreResult
  /** 页面识别结果 */
  page_recognition: PageRecognitionResult
}

// API调用状态枚举
export enum ApiCallStatus {
  Idle = 'idle',
  Loading = 'loading',
  Success = 'success',
  Error = 'error'
}

// API错误接口
export interface ApiError {
  /** 错误消息 */
  message: string
  /** 错误代码（可选） */
  code?: string
  /** 详细错误信息（可选） */
  details?: string
}

// 装备识别状态接口
export interface EquipmentRecognitionState {
  /** 当前状态 */
  status: ApiCallStatus
  /** 装备数据 */
  equipment: Equipment | null
  /** 评分结果 */
  scoreResult: ScoreResult | null
  /** 页面识别结果 */
  pageRecognition: PageRecognitionResult | null
  /** 错误信息 */
  error: ApiError | null
  /** 最后更新时间 */
  lastUpdated: Date | null
}
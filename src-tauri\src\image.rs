use image::{DynamicImage, Rgb, RgbImage, GenericImageView};
use anyhow::{Context, Result};
use std::path::{Path, PathBuf};
use serde::{Deserialize, Serialize};

use crate::config::ConfigService;
use crate::detection::PageType;

/// 图像区域定义
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ImageRegion {
    pub x: u32,
    pub y: u32,
    pub width: u32,
    pub height: u32,
}

impl ImageRegion {
    pub fn new(x: u32, y: u32, width: u32, height: u32) -> Self {
        Self { x, y, width, height }
    }
}

/// 图像处理结果
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ImageProcessingResult {
    pub processed_image_path: PathBuf,
    pub regions_extracted: Vec<ImageRegion>,
}

/// 图像处理服务
pub struct ImageService {
    config: ConfigService,
}

impl ImageService {
    /// 创建新的图像处理服务
    pub fn new() -> Result<Self> {
        let config = ConfigService::new("config.json".to_string());
        Ok(Self { config })
    }

    /// 使用指定配置创建图像处理服务
    pub fn with_config(config: ConfigService) -> Self {
        Self { config }
    }

    /// 验证图像分辨率是否为1280x720
    pub fn validate_resolution(&self, image: &DynamicImage) -> Result<()> {
        let (width, height) = image.dimensions();
        if width != 1280 || height != 720 {
            return Err(anyhow::anyhow!(
                "图像分辨率不正确，期望1280x720，实际{}x{}",
                width, height
            ));
        }
        Ok(())
    }

    /// 处理不同页面的图像预处理
    pub async fn process_page_image(&self, image_path: &Path, page_type: &PageType) -> Result<ImageProcessingResult> {
        let image = image::open(image_path)
            .context("加载图像失败")?;
        
        self.validate_resolution(&image)?;

        let (processed_image, regions) = match page_type {
            PageType::Backpack => self.process_backpack_page(&image).await?,
            PageType::Enhancement => self.process_enhancement_page(&image).await?,
            PageType::Recast => self.process_recast_page(&image).await?,
            PageType::Other => return Err(anyhow::anyhow!("不支持的页面类型")),
        };

        // 保存处理后的图像
        let output_path = Path::new("src-tauri/screenshot/processed_image.png");
        processed_image.save(output_path)
            .context("保存处理后的图像失败")?;

        Ok(ImageProcessingResult {
            processed_image_path: output_path.to_path_buf(),
            regions_extracted: regions,
        })
    }

    /// 处理背包页面
    async fn process_backpack_page(&self, image: &DynamicImage) -> Result<(DynamicImage, Vec<ImageRegion>)> {
        // 背包页面截取区域：[y_min = 165；y_max = y_min + 420； x_min = 875；x_max = x_min + 350]
        let main_region = ImageRegion::new(875, 165, 350, 420);
        let cropped = self.extract_region(image, &main_region)?;

        // 保留的四个关键区域
        let keep_regions = vec![
            ImageRegion::new(45, 0, 45, 35),      // 稀有度区域
            ImageRegion::new(90, 15, 80, 32),     // 部位区域
            ImageRegion::new(0, 155, 350, 180),   // 属性区域
            ImageRegion::new(40, 385, 200, 35),   // 套装区域
        ];

        // 创建掩码，将非关键区域设为黑色
        let processed = self.apply_region_mask(&cropped, &keep_regions)?;

        Ok((processed, keep_regions))
    }

    /// 处理强化页面
    async fn process_enhancement_page(&self, image: &DynamicImage) -> Result<(DynamicImage, Vec<ImageRegion>)> {
        // 强化页面截取区域：[y_min = 80；y_max = y_min + 400； x_min = 30；x_max = x_min + 340]
        let main_region = ImageRegion::new(30, 80, 340, 400);
        let cropped = self.extract_region(image, &main_region)?;

        // 保留的四个关键区域
        let keep_regions = vec![
            ImageRegion::new(60, 0, 45, 35),      // 稀有度区域
            ImageRegion::new(105, 17, 75, 23),    // 部位区域
            ImageRegion::new(0, 135, 340, 215),   // 属性区域
            ImageRegion::new(43, 365, 200, 35),   // 套装区域
        ];

        // 创建掩码，将非关键区域设为黑色
        let processed = self.apply_region_mask(&cropped, &keep_regions)?;

        Ok((processed, keep_regions))
    }

    /// 处理重铸页面
    async fn process_recast_page(&self, image: &DynamicImage) -> Result<(DynamicImage, Vec<ImageRegion>)> {
        // 重铸页面截取区域：[y_min = 490；y_max = y_min + 135； x_min = 280；x_max = x_min + 490]
        let main_region = ImageRegion::new(280, 490, 490, 135);
        let cropped = self.extract_region(image, &main_region)?;

        // 需要涂黑的干扰区域：[y_min = 0；y_max = y_min + 135； x_min = 370；x_max = x_min + 60]
        let blackout_regions = vec![
            ImageRegion::new(370, 0, 60, 135),
        ];

        // 将干扰区域涂黑
        let processed = self.apply_blackout_mask(&cropped, &blackout_regions)?;

        Ok((processed, vec![main_region]))
    }

    /// 提取图像区域
    pub fn extract_region(&self, image: &DynamicImage, region: &ImageRegion) -> Result<DynamicImage> {
        let cropped = image.crop_imm(region.x, region.y, region.width, region.height);
        Ok(cropped)
    }

    /// 应用区域掩码，保留指定区域，其余区域设为黑色
    fn apply_region_mask(&self, image: &DynamicImage, keep_regions: &[ImageRegion]) -> Result<DynamicImage> {
        let (width, height) = image.dimensions();
        let mut result = RgbImage::new(width, height);

        // 将整个图像设为黑色
        for pixel in result.pixels_mut() {
            *pixel = Rgb([0, 0, 0]);
        }

        let rgb_image = image.to_rgb8();

        // 复制保留区域的像素
        for region in keep_regions {
            for y in region.y..std::cmp::min(region.y + region.height, height) {
                for x in region.x..std::cmp::min(region.x + region.width, width) {
                    if let Some(src_pixel) = rgb_image.get_pixel_checked(x, y) {
                        result.put_pixel(x, y, *src_pixel);
                    }
                }
            }
        }

        Ok(DynamicImage::ImageRgb8(result))
    }

    /// 应用黑色掩码，将指定区域涂黑
    fn apply_blackout_mask(&self, image: &DynamicImage, blackout_regions: &[ImageRegion]) -> Result<DynamicImage> {
        let mut result = image.to_rgb8();

        // 将指定区域涂黑
        for region in blackout_regions {
            for y in region.y..std::cmp::min(region.y + region.height, result.height()) {
                for x in region.x..std::cmp::min(region.x + region.width, result.width()) {
                    result.put_pixel(x, y, Rgb([0, 0, 0]));
                }
            }
        }

        Ok(DynamicImage::ImageRgb8(result))
    }
}

/// Tauri命令接口

/// 处理页面图像
#[tauri::command]
pub async fn process_page_image(image_path: String, page_type: String) -> Result<ImageProcessingResult, String> {
    let processor = ImageService::new()
        .map_err(|e| e.to_string())?;
    
    let path = Path::new(&image_path);
    let page_type = match page_type.as_str() {
        "Backpack" => PageType::Backpack,
        "Enhancement" => PageType::Enhancement,
        "Recast" => PageType::Recast,
        "Other" => PageType::Other,
        _ => return Err("无效的页面类型".to_string()),
    };
    
    processor.process_page_image(path, &page_type).await
        .map_err(|e| e.to_string())
}
